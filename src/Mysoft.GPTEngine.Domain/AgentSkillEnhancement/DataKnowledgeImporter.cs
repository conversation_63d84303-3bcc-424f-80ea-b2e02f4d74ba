using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Milvus.Client;
using Mysoft.GPTEngine.Common.Rabbitmq.Const;
using Mysoft.GPTEngine.Domain.Extensions;
using Mysoft.GPTEngine.Domain.Repositories;
using Mysoft.GPTEngine.Domain.Shared;
using Newtonsoft.Json;
using System.Text.Json;
using Mysoft.GPTEngine.Domain.Entity;

namespace Mysoft.GPTEngine.Domain.AgentSkillEnhancement
{


    /// <summary>
    /// 数据知识库导入器，用于查询TypeEnum=3的知识库全量数据
    /// </summary>
    public class DataKnowledgeImporter
    {
        private readonly Kernel _kernel;
        private readonly ILogger<DataKnowledgeImporter> _logger;
        private readonly KnowledgeFileSectionRepository _knowledgeFileSectionRepository;
        private readonly KnowledgeRepository _knowledgeRepository;
        private readonly IKnowledgeDomainService _knowledgeDomainService;
        private readonly IMysoftContextFactory _mysoftContextFactory;
        private readonly IMysoftApiService _apiService;
        private readonly IConfigurationService _configurationService;
        private readonly AgentSkillEventHelper _eventHelper;
        // 暂时屏蔽BaseDataFixParamHelper的使用
        // private readonly BaseDataFixParamHelper _baseDataFixParamHelper;

        public DataKnowledgeImporter(Kernel kernel, IServiceProvider serviceProvider)
        {
            _kernel = kernel;
            _logger = serviceProvider.GetService<ILoggerFactory>().CreateLogger<DataKnowledgeImporter>();
            _knowledgeFileSectionRepository = serviceProvider.GetService<KnowledgeFileSectionRepository>() ??
                                             throw new ArgumentNullException(nameof(KnowledgeFileSectionRepository));
            _knowledgeRepository = serviceProvider.GetService<KnowledgeRepository>() ??
                                   throw new ArgumentNullException(nameof(KnowledgeRepository));
            _knowledgeDomainService = serviceProvider.GetService<IKnowledgeDomainService>() ??
                                     throw new ArgumentNullException(nameof(IKnowledgeDomainService));
            _mysoftContextFactory = serviceProvider.GetService<IMysoftContextFactory>() ??
                                   throw new ArgumentNullException(nameof(IMysoftContextFactory));
            _apiService = serviceProvider.GetService<IMysoftApiService>();
            _configurationService = serviceProvider.GetService<IConfigurationService>();
            _eventHelper = new AgentSkillEventHelper(serviceProvider.GetService<IHttpContextAccessor>());
            // 暂时屏蔽BaseDataFixParamHelper的使用
            // _baseDataFixParamHelper = serviceProvider.GetService<BaseDataFixParamHelper>() ??
            //                          throw new ArgumentNullException(nameof(BaseDataFixParamHelper));
        }

        /// <summary>
        /// 批量查询多个知识库的向量数据库全量数据并打印具体内容，汇总处理Metadata
        /// </summary>
        /// <param name="knowledgeCodes">知识库代码数组</param>
        /// <param name="toolTitleMapping">工具标题映射Dictionary</param>
        public async Task QueryAllVectorDatabaseDataAsync(string[] knowledgeCodes, ConcurrentDictionary<string, string> toolTitleMapping = null)
        {
            if (knowledgeCodes == null || knowledgeCodes.Length == 0)
            {
                _logger.LogInformation("[QueryAllVectorDatabaseDataAsync] 没有需要查询的知识库");
                return;
            }

            _logger.LogInformation("[QueryAllVectorDatabaseDataAsync] 开始批量查询 {count} 个知识库的向量数据库全量数据", knowledgeCodes.Length);

            // 收集所有知识库的KnowledgeFileSectionEntity数据
            var allSectionEntities = new List<Entity.KnowledgeFileSectionEntity>();

            foreach (var knowledgeCode in knowledgeCodes)
            {
                try
                {
                    var sectionEntities = await QuerySingleVectorDatabaseDataAsync(knowledgeCode);
                    if (sectionEntities != null && sectionEntities.Any())
                    {
                        allSectionEntities.AddRange(sectionEntities);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "[QueryAllVectorDatabaseDataAsync] 查询知识库 {knowledgeCode} 时发生异常: {message}", knowledgeCode, ex.Message);
                }
            }

            // 汇总处理Metadata数据并注入工具到kernel
            await ProcessMetadataAndInjectToolsAsync(allSectionEntities, toolTitleMapping);

            _logger.LogInformation("[QueryAllVectorDatabaseDataAsync] 批量查询完成");
        }

        /// <summary>
        /// 查询单个知识库的向量数据库全量数据并返回KnowledgeFileSectionEntity列表
        /// </summary>
        /// <param name="knowledgeCode">知识库代码</param>
        /// <returns>KnowledgeFileSectionEntity列表</returns>
        private async Task<List<Entity.KnowledgeFileSectionEntity>> QuerySingleVectorDatabaseDataAsync(string knowledgeCode)
        {
            try
            {
                _logger.LogInformation("[QuerySingleVectorDatabaseDataAsync] 开始查询知识库 {knowledgeCode} 的向量库全量数据", knowledgeCode);

                // 直接使用Milvus客户端查询全量数据，因为KnowledgeDomainService的方法都是基于相似度搜索的
                var mysoftContext = _mysoftContextFactory.GetMysoftContext();
                var milvusClient = mysoftContext.MemoryStore.CreateMilvusClient(mysoftContext.TenantCode);

                // 检查集合是否存在
                var collectionExists = await milvusClient.HasCollectionAsync(knowledgeCode);
                if (!collectionExists)
                {
                    _logger.LogWarning("[QuerySingleVectorDatabaseDataAsync] 向量库集合不存在: {knowledgeCode}", knowledgeCode);
                    return new List<Entity.KnowledgeFileSectionEntity>();
                }

                var milvusCollection = milvusClient.GetCollection(knowledgeCode);

                // 查询全量数据 - 使用有效的表达式查询所有数据
                var queryParameters = new QueryParameters();
                queryParameters.OutputFields.Add("id");
                queryParameters.OutputFields.Add("metadata");
                queryParameters.ConsistencyLevel = ConsistencyLevel.Strong;

                // 使用有效表达式查询所有数据，id != "" 表示查询所有非空ID的记录
                var allData = await milvusCollection.QueryAsync("id != \"\"", queryParameters);

                _logger.LogInformation("[QuerySingleVectorDatabaseDataAsync] 知识库 {knowledgeCode} 向量库查询完成，共 {fieldCount} 个字段", knowledgeCode, allData.Count);

                // 提取ID列表
                var idFieldData = allData.FirstOrDefault(f => f.FieldName == "id") as FieldData<string>;
                if (idFieldData == null || !idFieldData.Data.Any())
                {
                    _logger.LogInformation("[QuerySingleVectorDatabaseDataAsync] 知识库 {knowledgeCode} 向量库中没有数据", knowledgeCode);
                    return new List<Entity.KnowledgeFileSectionEntity>();
                }

                var allIds = idFieldData.Data.ToList();
                _logger.LogInformation("[QuerySingleVectorDatabaseDataAsync] 知识库 {knowledgeCode} 向量库中共有 {count} 条记录", knowledgeCode, allIds.Count);

                // 将string类型的ID转换为Guid类型
                var guidIds = new List<Guid>();
                foreach (var id in allIds)
                {
                    if (Guid.TryParse(id, out var guidId))
                    {
                        guidIds.Add(guidId);
                    }
                }

                // 从gpt_knowledgefilesection表获取具体内容
                var sectionEntities = await _knowledgeFileSectionRepository.GetListAsync(x => guidIds.Contains(x.KnowledgeFileSectionGUID) && x.Disable == 0);

                if (sectionEntities == null || !sectionEntities.Any())
                {
                    _logger.LogWarning("[QuerySingleVectorDatabaseDataAsync] 知识库 {knowledgeCode} 在gpt_knowledgefilesection表中未找到对应数据", knowledgeCode);
                    return new List<Entity.KnowledgeFileSectionEntity>();
                }

                _logger.LogInformation("[QuerySingleVectorDatabaseDataAsync] 知识库 {knowledgeCode} 从gpt_knowledgefilesection表获取到 {count} 条有效记录", knowledgeCode, sectionEntities.Count);

                // 打印所有数据的详细信息
                await PrintKnowledgeDataAsync(knowledgeCode, sectionEntities);

                return sectionEntities;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[QuerySingleVectorDatabaseDataAsync] 查询知识库 {knowledgeCode} 向量库全量数据时发生异常: {message}", knowledgeCode, ex.Message);
                return new List<Entity.KnowledgeFileSectionEntity>();
            }
        }

        /// <summary>
        /// 打印知识库数据的详细信息
        /// </summary>
        /// <param name="knowledgeCode">知识库代码</param>
        /// <param name="sectionEntities">知识库片段实体列表</param>
        private async Task PrintKnowledgeDataAsync(string knowledgeCode, List<Entity.KnowledgeFileSectionEntity> sectionEntities)
        {
            _logger.LogInformation("[PrintKnowledgeDataAsync] ========== 知识库 {knowledgeCode} 全量数据开始 ==========", knowledgeCode);
            
            for (int i = 0; i < sectionEntities.Count; i++)
            {
                var section = sectionEntities[i];
                
                _logger.LogInformation("[PrintKnowledgeDataAsync] 记录 {index}/{total}:", i + 1, sectionEntities.Count);
                _logger.LogInformation("  - ID: {id}", section.KnowledgeFileSectionGUID);
                _logger.LogInformation("  - 标题: {title}", section.ParagraphTitle ?? "无标题");
                _logger.LogInformation("  - 内容长度: {length} 字符", section.Content?.Length ?? 0);
                
                if (!string.IsNullOrWhiteSpace(section.Content))
                {
                    // 打印内容的前200个字符作为预览
                    var contentPreview = section.Content.Length > 200 
                        ? section.Content.Substring(0, 200) + "..." 
                        : section.Content;
                    _logger.LogInformation("  - 内容预览: {content}", contentPreview);
                }
                
                if (!string.IsNullOrWhiteSpace(section.Metadata))
                {
                    _logger.LogInformation("  - Metadata: {metadata}", section.Metadata);
                }
                
                _logger.LogInformation("  - 文件GUID: {fileGuid}", section.KnowledgeFileGUID);
                _logger.LogInformation("  - 创建时间: {createTime}", section.CreatedTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "未知");
                _logger.LogInformation("  ---");
            }

            _logger.LogInformation("[PrintKnowledgeDataAsync] ========== 知识库 {knowledgeCode} 全量数据结束 ==========", knowledgeCode);
            
            // 添加异步操作以避免编译警告
            await Task.CompletedTask;
        }

        /// <summary>
        /// 处理Metadata数据，根据ID去重并生成工具列表，然后注入到kernel中
        /// </summary>
        /// <param name="sectionEntities">知识库片段实体列表</param>
        /// <param name="toolTitleMapping">工具标题映射Dictionary</param>
        private async Task ProcessMetadataAndInjectToolsAsync(List<Entity.KnowledgeFileSectionEntity> sectionEntities, ConcurrentDictionary<string, string> toolTitleMapping)
        {
            try
            {
                _logger.LogInformation("[ProcessMetadataAsync] 开始处理 {count} 条记录的Metadata数据", sectionEntities.Count);

                // 收集所有有效的Metadata和对应的section信息
                var metadataList = new List<Dictionary<string, object>>();
                var sectionTitleMapping = new Dictionary<string, string>(); // id -> ParagraphTitle
                var processedIds = new HashSet<string>();

                foreach (var entity in sectionEntities)
                {
                    if (string.IsNullOrWhiteSpace(entity.Metadata))
                        continue;

                    try
                    {
                        var metadata = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(entity.Metadata);
                        if (metadata != null && metadata.ContainsKey("id"))
                        {
                            var id = metadata["id"]?.ToString();
                            if (!string.IsNullOrWhiteSpace(id) && !processedIds.Contains(id))
                            {
                                processedIds.Add(id);
                                metadataList.Add(metadata);

                                // 收集section的ParagraphTitle作为工具标题
                                var paragraphTitle = !string.IsNullOrWhiteSpace(entity.ParagraphTitle) ? entity.ParagraphTitle : id;
                                sectionTitleMapping[id] = paragraphTitle;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "[ProcessMetadataAsync] 解析Metadata失败: {metadata}", entity.Metadata);
                    }
                }

                _logger.LogInformation("[ProcessMetadataAsync] 去重后有效的Metadata数据: {count} 条", metadataList.Count);

                // 生成工具列表
                var toolList = new List<object>();
                foreach (var metadata in metadataList)
                {
                    try
                    {
                        var tool = GenerateToolFromMetadata(metadata, sectionTitleMapping);
                        if (tool != null)
                        {
                            toolList.Add(tool);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "[ProcessMetadataAsync] 生成工具时发生异常: {metadata}", System.Text.Json.JsonSerializer.Serialize(metadata));
                    }
                }

                _logger.LogInformation("[ProcessMetadataAndInjectToolsAsync] 成功生成 {count} 个工具", toolList.Count);

                // 打印生成的工具列表
                await PrintToolListAsync(toolList);

                // 注入工具到kernel
                await InjectToolsToKernelAsync(toolList, toolTitleMapping, sectionTitleMapping);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[ProcessMetadataAndInjectToolsAsync] 处理Metadata时发生异常: {message}", ex.Message);
            }
        }

        /// <summary>
        /// 根据Metadata生成工具对象
        /// </summary>
        /// <param name="metadata">Metadata字典</param>
        /// <param name="sectionTitleMapping">section标题映射</param>
        /// <returns>工具对象</returns>
        private object GenerateToolFromMetadata(Dictionary<string, object> metadata, Dictionary<string, string> sectionTitleMapping)
        {
            try
            {
                var id = metadata["id"]?.ToString();
                if (string.IsNullOrWhiteSpace(id))
                    return null;

                // 解析params参数
                var paramsJson = metadata.ContainsKey("params") ? metadata["params"]?.ToString() : "[]";
                var paramsList = new List<Dictionary<string, object>>();

                try
                {
                    if (!string.IsNullOrWhiteSpace(paramsJson))
                    {
                        paramsList = System.Text.Json.JsonSerializer.Deserialize<List<Dictionary<string, object>>>(paramsJson) ?? new List<Dictionary<string, object>>();
                    }
                }
                catch
                {
                    paramsList = new List<Dictionary<string, object>>();
                }

                // 生成入参schema
                var properties = new Dictionary<string, object>();
                foreach (var param in paramsList)
                {
                    var name = param.ContainsKey("name") ? param["name"]?.ToString() : "";
                    var description = param.ContainsKey("description") ? param["description"]?.ToString() : "";

                    if (!string.IsNullOrWhiteSpace(name))
                    {
                        properties[name] = new
                        {
                            type = "string",
                            description = description ?? ""
                        };
                    }
                }

                var inputSchema = new
                {
                    type = "object",
                    properties = properties,
                    required = new string[0]
                };

                // 获取title，优先使用section.ParagraphTitle，然后是dashboard_name，最后是dashboard_id
                var title = "";
                if (sectionTitleMapping != null && sectionTitleMapping.ContainsKey(id))
                {
                    title = sectionTitleMapping[id];
                }
                else if (metadata.ContainsKey("dashboard_name"))
                {
                    title = metadata["dashboard_name"]?.ToString() ?? "";
                }
                else if (metadata.ContainsKey("dashboard_id"))
                {
                    title = metadata["dashboard_id"]?.ToString() ?? "";
                }

                var tool = new
                {
                    id = id,
                    name = $"scene_{id.Replace("-", "_")}",
                    title = title,
                    description = metadata.ContainsKey("description") ? metadata["description"]?.ToString() ?? "" : "",
                    input_schema = inputSchema
                };

                return tool;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[GenerateToolFromMetadata] 生成工具时发生异常");
                return null;
            }
        }

        /// <summary>
        /// 打印生成的工具列表
        /// </summary>
        /// <param name="toolList">工具列表</param>
        private async Task PrintToolListAsync(List<object> toolList)
        {
            try
            {
                _logger.LogInformation("[PrintToolListAsync] ========== 生成的工具列表开始 ==========");

                for (int i = 0; i < toolList.Count; i++)
                {
                    var tool = toolList[i];
                    var toolJson = System.Text.Json.JsonSerializer.Serialize(tool, new JsonSerializerOptions
                    {
                        WriteIndented = true,
                        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                    });

                    _logger.LogInformation("[PrintToolListAsync] 工具 {index}/{total}:\n{toolJson}", i + 1, toolList.Count, toolJson);
                }

                _logger.LogInformation("[PrintToolListAsync] ========== 生成的工具列表结束 ==========");

                // 添加异步操作以避免编译警告
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[PrintToolListAsync] 打印工具列表时发生异常: {message}", ex.Message);
            }
        }

        /// <summary>
        /// 将工具列表注入到kernel中
        /// </summary>
        /// <param name="toolList">工具列表</param>
        /// <param name="toolTitleMapping">工具标题映射Dictionary</param>
        /// <param name="sectionTitleMapping">section标题映射</param>
        private async Task InjectToolsToKernelAsync(List<object> toolList, ConcurrentDictionary<string, string> toolTitleMapping, Dictionary<string, string> sectionTitleMapping)
        {
            try
            {
                if (toolList == null || !toolList.Any())
                {
                    _logger.LogInformation("[InjectToolsToKernelAsync] 没有工具需要注入");
                    return;
                }

                _logger.LogInformation("[InjectToolsToKernelAsync] 开始注入 {count} 个工具到kernel", toolList.Count);

                var functions = new List<KernelFunction>();
                foreach (var toolObj in toolList)
                {
                    try
                    {
                        // 将工具对象转换为动态对象以便访问属性
                        var toolJson = System.Text.Json.JsonSerializer.Serialize(toolObj);
                        dynamic tool = JsonConvert.DeserializeObject(toolJson);

                        string id = tool.id;
                        string name = tool.name;
                        string title = tool.title;
                        string description = tool.description;
                        var inputSchema = tool.input_schema;

                        var parameters = new List<KernelParameterMetadata>();
                        if (inputSchema?.properties != null)
                        {
                            foreach (var prop in inputSchema.properties)
                            {
                                parameters.Add(new KernelParameterMetadata(prop.Name)
                                {
                                    Description = prop.Value?.description?.ToString() ?? ""
                                });
                            }
                        }

                        // 确保工具名称不超过64个字符（考虑插件名长度）
                        var truncatedName = ToolNameHelper.TruncateFunctionName(name, "DataKnowledgeTools");
                        if (truncatedName != name)
                        {
                            _logger.LogWarning("[InjectToolsToKernelAsync] 工具名称过长，已截断: {originalName} -> {truncatedName}", name, truncatedName);
                        }

                        // 定义本地函数
                        async Task<string> GetDataFunc(KernelArguments arguments, CancellationToken ct)
                        {
                            var dict = new Dictionary<string, string>();
                            if (inputSchema?.properties != null)
                            {
                                foreach (var prop in inputSchema.properties)
                                {
                                    string key = prop.Name;
                                    var value = arguments.TryGetValue(key, out var v) ? v?.ToString() : "";

                                    if (!string.IsNullOrEmpty(value))
                                        dict[key] = value;
                                }
                            }

                            // 暂时屏蔽基础资料库修正参数的调用
                            // dict = await _baseDataFixParamHelper.FixParams(dict, id);

                            return await GetData(id, name, title, JsonConvert.SerializeObject(dict), ct);
                        }

                        functions.Add(KernelFunctionFactory.CreateFromMethod(
                            method: GetDataFunc,
                            functionName: truncatedName,
                            description: description,
                            parameters: parameters
                        ));

                        // 添加工具标题映射，使用section.ParagraphTitle作为显示标题
                        if (toolTitleMapping != null && sectionTitleMapping != null && sectionTitleMapping.ContainsKey(id))
                        {
                            string toolKey = $"DataKnowledgeTools.{truncatedName}";
                            string displayTitle = sectionTitleMapping[id];
                            toolTitleMapping.TryAdd(toolKey, displayTitle);
                            _logger.LogInformation("[InjectToolsToKernelAsync] 添加数据知识库工具标题映射: {toolKey} -> {title}", toolKey, displayTitle);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "[InjectToolsToKernelAsync] 处理单个工具时发生异常: {tool}", System.Text.Json.JsonSerializer.Serialize(toolObj));
                    }
                }

                if (functions.Any())
                {
                    var plugin = KernelPluginFactory.CreateFromFunctions("DataKnowledgeTools", null, functions);
                    _kernel.Plugins.Add(plugin);
                    _logger.LogInformation("[InjectToolsToKernelAsync] 成功注入 {count} 个工具到kernel", functions.Count);
                }
                else
                {
                    _logger.LogWarning("[InjectToolsToKernelAsync] 没有有效的工具可以注入");
                }

                // 添加异步操作以避免编译警告
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[InjectToolsToKernelAsync] 注入工具到kernel时发生异常: {message}", ex.Message);
            }
        }



        /// <summary>
        /// 调用数据平台获取数据
        /// </summary>
        /// <param name="id">场景ID</param>
        /// <param name="name">工具名称</param>
        /// <param name="title">工具标题</param>
        /// <param name="params">参数JSON字符串</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>数据结果</returns>
        private async Task<string> GetData(
            string id,
            string name,
            string title,
            string @params,
            CancellationToken cancellationToken = default)
        {
            var dpUrl = _configurationService.GetConfigurationItemByKey(EMCConfigConst.DpUrl).TrimEnd('/');
            var url = $"{dpUrl}/openapi/ai/get_data";

            try
            {
                var requestBody = JsonConvert.SerializeObject(new Dictionary<string, string>
                {
                    { "scene_id", id },
                    { "params", @params }
                });

                _logger.LogInformation("[GetData] url: {url}, requestBody: {requestBody}", url, requestBody);

                var response = await _apiService.PostAsync(url, requestBody, cancellationToken);
                var result = JsonConvert.DeserializeObject<dynamic>(response);
                if (result != null && result.success == true)
                {
                    Dictionary<string, object> data = result.data.ToObject<Dictionary<string, object>>();
                    _logger.LogInformation("[GetData] 接口成功. {data}", data);

                    
                    return JsonConvert.SerializeObject(data);
                }

                _logger.LogError("[GetData] 接口失败，响应: {response}", response);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "[GetData] 接口异常");
            }

            return "FAILED";
        }
    }
}
